import { Form, Input } from "antd";
import { useLogin } from "auth/hooks/useLogin";
import { LoginForm } from "auth/types/LoginForm";
import Button from "components/Button";
import ValidateMessages from "shared/lib/consts/validates";
import { StatusType } from "shared/lib/types/StatusType";
import shape1 from "auth/assets/shape1.svg";
import shape2 from "auth/assets/shape2.svg";
import shape3 from "auth/assets/shape3.svg";
import shape4 from "auth/assets/shape4.svg";
import logo from "shared/assets/logo.svg";

const LoginPage = () => {
  const { mutateAsync, isPending } = useLogin();
  const [form] = Form.useForm<LoginForm>();

  const onFinish = async (values: LoginForm) => {
    const response = await mutateAsync(values);

    console.log(response);

    if (response.status === StatusType.ERROR) {
      return form.setFields([{ name: "password", errors: [response.error] }]);
    }
  };
  return (
    <div className="h-screen bg-[#F0F3F7] flex items-center justify-center p-4">
      <div className="flex flex-col gap-[44px] items-center max-w-[370px] w-full relative z-10">
        <div className="flex flex-col gap-3 text-center">
          <div className="flex items-center gap-3">
            <img src={logo} alt="Eletcom Logo" />
            <h4 className="text-[33px] leading-[33px] text-[#000000D9]">
              Eletcom Admin
            </h4>
          </div>

          <p className="text-[14px] leading-[22px] text-[#00000073]">
            Вход в систему управления инфраструктурой.
          </p>
        </div>
        <Form
          className="flex flex-col gap-4 w-full"
          name="login-form"
          onFinish={onFinish}
          form={form}
        >
          <Form.Item
            name="email"
            rules={[{ message: ValidateMessages.Email, required: true }]}
          >
            <Input
              className="h-[40px] !rounded-none"
              disabled={isPending}
              placeholder="E-Mail"
            />
          </Form.Item>
          <Form.Item
            name="password"
            rules={[{ message: ValidateMessages.Password, required: true }]}
          >
            <Input.Password
              className="h-[40px] !rounded-none"
              disabled={isPending}
              placeholder="Пароль"
            />
          </Form.Item>
          <Form.Item>
            <Button
              htmlType="submit"
              loading={isPending}
              className="w-full !h-[40px] !rounded-none text-[16px] leading-[24px]"
              type="primary"
            >
              Войти
            </Button>
          </Form.Item>
        </Form>
      </div>

      <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
        <img
          className="absolute top-[100px] left-[176px]"
          src={shape1}
          alt="Shape 1"
        />
        <img
          className="absolute bottom-[100px] left-[80px]"
          src={shape2}
          alt="Shape 2"
        />
        <img
          className="absolute top-[74px] right-[260px]"
          src={shape3}
          alt="Shape 3"
        />
        <img
          className="absolute bottom-[10px] right-[-10px]"
          src={shape4}
          alt="Shape 4"
        />
      </div>
    </div>
  );
};

export default LoginPage;
