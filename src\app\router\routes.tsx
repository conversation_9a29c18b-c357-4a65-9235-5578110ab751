import LoginPage from "auth/pages";
import Subscribers from "modules/subscribers/pages";
import { ConnectionRequests, SupportRequests } from "service-requests/pages";
import Infractructure from "infrastructure/pages";
import MapPage from "map/pages";
import Employees from "employees/pages";
import ActivityLog from "activity-log/pages";

interface IRoute {
  path: string;
  element: React.ReactNode;
}

interface IRoutes extends IRoute {
  children?: IRoute[];
}

const INIT_ROUTES: IRoutes[] = [
  { path: "/subscribers", element: <Subscribers /> },
  { path: "/service-requests/connection", element: <ConnectionRequests /> },
  { path: "/service-requests/support", element: <SupportRequests /> },
  { path: "/infrastructure", element: <Infractructure /> },
  { path: "/employees", element: <Employees /> },
  { path: "/activity-log", element: <ActivityLog /> },
  { path: "/map", element: <MapPage /> },
];

const AUTH_ROUTES: IRoutes[] = [{ path: "/login", element: <LoginPage /> }];

export default { INIT_ROUTES, AUTH_ROUTES };
