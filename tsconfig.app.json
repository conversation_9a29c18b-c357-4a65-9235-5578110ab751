{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": "src", "paths": {"app/*": ["app/*"], "shared/*": ["shared/*"], "components/*": ["components/*"], "auth/*": ["modules/auth/*"], "subscribers": ["modules/subscribers/*"], "service-requests": ["modules/service-requests/*"], "infrastructure": ["modules/infrastructure/*"], "map": ["modules/map/*"], "employees": ["modules/employees/*"], "activity-log": ["modules/activity-log/*"]}}, "include": ["src"]}